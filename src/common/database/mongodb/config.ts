import { registerAs } from '@nestjs/config';
import { IsString, IsOptional, validateSync } from 'class-validator';
import { plainToClass } from 'class-transformer';

export type MongoConfig = {
  uri: string;
  dbName?: string;
};

class EnvironmentVariablesValidator {
  @IsString()
  MONGODB_URI: string;

  @IsString()
  @IsOptional()
  MONGODB_DB_NAME: string;
}

function validateConfig(config: Record<string, unknown>) {
  const validatedConfig = plainToClass(EnvironmentVariablesValidator, config, {
    enableImplicitConversion: true,
  });
  const errors = validateSync(validatedConfig, {
    skipMissingProperties: false,
  });

  if (errors.length > 0) {
    throw new Error(errors.toString());
  }
  return validatedConfig;
}

export default registerAs<MongoConfig>('mongodb', () => {
  validateConfig(process.env);

  return {
    uri: process.env.MONGODB_URI!,
    dbName: process.env.MONGODB_DB_NAME,
  };
});
