import { Connection } from 'mongoose';
import { IMigration } from '../migration.interface';

export class InitChatCollectionsMigration implements IMigration {
  name = 'InitChatCollections';
  version = '001';

  async up(connection: Connection): Promise<void> {
    const db = connection.db!;

    // Create chatrooms collection
    await db.createCollection('chatrooms');
    const chatroomsCollection = db.collection('chatrooms');

    // Create indexes for chatrooms
    await chatroomsCollection.createIndex({ name: 1 });
    await chatroomsCollection.createIndex({ 'participants.userId': 1 });
    await chatroomsCollection.createIndex({ createdBy: 1 });
    await chatroomsCollection.createIndex({ isPrivate: 1 });
    await chatroomsCollection.createIndex({ createdAt: -1 });

    // Compound indexes for chatrooms
    await chatroomsCollection.createIndex({
      'participants.userId': 1,
      isPrivate: 1,
    });
    await chatroomsCollection.createIndex({ createdBy: 1, createdAt: -1 });

    console.log('✓ Created chatrooms collection with indexes');

    // Create messages collection
    await db.createCollection('messages');
    const messagesCollection = db.collection('messages');

    // Create indexes for messages
    await messagesCollection.createIndex({ chatroomId: 1, timestamp: -1 });
    await messagesCollection.createIndex({ 'user.userId': 1, timestamp: -1 });
    await messagesCollection.createIndex({ chatroomId: 1, messageType: 1 });
    await messagesCollection.createIndex({
      chatroomId: 1,
      isDeleted: 1,
      timestamp: -1,
    });
    await messagesCollection.createIndex({ replyTo: 1 });

    // Compound indexes for messages
    await messagesCollection.createIndex({
      chatroomId: 1,
      isDeleted: 1,
      timestamp: -1,
    });
    await messagesCollection.createIndex({
      'user.userId': 1,
      chatroomId: 1,
      timestamp: -1,
    });

    // Text index for message content search
    await messagesCollection.createIndex({ content: 'text' });

    console.log('✓ Created messages collection with indexes');
  }

  async down(connection: Connection): Promise<void> {
    const db = connection.db!;

    // Drop collections
    await db.dropCollection('messages');
    console.log('✓ Dropped messages collection');

    await db.dropCollection('chatrooms');
    console.log('✓ Dropped chatrooms collection');
  }
}
