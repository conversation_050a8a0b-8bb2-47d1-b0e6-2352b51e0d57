import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import {
  IMigration,
  IMigrationRecord,
  IMigrationRunner,
} from './migration.interface';

@Injectable()
export class MigrationRunnerService implements IMigrationRunner {
  private readonly logger = new Logger(MigrationRunnerService.name);
  private readonly migrationCollectionName = 'migrations';

  constructor(@InjectConnection() private readonly connection: Connection) {}

  private migrations: IMigration[] = [];

  registerMigration(migration: IMigration): void {
    this.migrations.push(migration);
    // Sort migrations by version
    this.migrations.sort((a, b) => a.version.localeCompare(b.version));
  }

  async run(): Promise<void> {
    this.logger.log('Starting MongoDB migrations...');

    // Ensure migrations collection exists
    await this.ensureMigrationsCollection();

    // Get executed migrations
    const executedMigrations = await this.getExecutedMigrations();
    const executedNames = new Set(executedMigrations.map((m) => m.name));

    // Run pending migrations
    for (const migration of this.migrations) {
      if (!executedNames.has(migration.name)) {
        this.logger.log(
          `Running migration: ${migration.name} (${migration.version})`,
        );

        try {
          await migration.up(this.connection);
          await this.recordMigration(migration);
          this.logger.log(`✓ Migration completed: ${migration.name}`);
        } catch (error) {
          this.logger.error(`✗ Migration failed: ${migration.name}`, error);
          throw error;
        }
      } else {
        this.logger.log(`⊘ Migration already executed: ${migration.name}`);
      }
    }

    this.logger.log('All migrations completed successfully');
  }

  async rollback(steps: number = 1): Promise<void> {
    this.logger.log(`Rolling back ${steps} migration(s)...`);

    const executedMigrations = await this.getExecutedMigrations();
    const migrationsToRollback = executedMigrations
      .sort((a, b) => b.executedAt.getTime() - a.executedAt.getTime())
      .slice(0, steps);

    for (const migrationRecord of migrationsToRollback) {
      const migration = this.migrations.find(
        (m) => m.name === migrationRecord.name,
      );

      if (!migration) {
        this.logger.warn(
          `Migration not found for rollback: ${migrationRecord.name}`,
        );
        continue;
      }

      this.logger.log(
        `Rolling back migration: ${migration.name} (${migration.version})`,
      );

      try {
        await migration.down(this.connection);
        await this.removeMigrationRecord(migration);
        this.logger.log(`✓ Rollback completed: ${migration.name}`);
      } catch (error) {
        this.logger.error(`✗ Rollback failed: ${migration.name}`, error);
        throw error;
      }
    }

    this.logger.log('Rollback completed successfully');
  }

  async status(): Promise<IMigrationRecord[]> {
    return await this.getExecutedMigrations();
  }

  private async ensureMigrationsCollection(): Promise<void> {
    const collections = await this.connection.db!.listCollections().toArray();
    const migrationCollectionExists = collections.some(
      (col) => col.name === this.migrationCollectionName,
    );

    if (!migrationCollectionExists) {
      await this.connection.db!.createCollection(this.migrationCollectionName);
      this.logger.log('Created migrations collection');
    }
  }

  private async getExecutedMigrations(): Promise<IMigrationRecord[]> {
    const collection = this.connection.db!.collection(
      this.migrationCollectionName,
    );
    return (await collection
      .find({})
      .sort({ executedAt: 1 })
      .toArray()) as unknown as IMigrationRecord[];
  }

  private async recordMigration(migration: IMigration): Promise<void> {
    const collection = this.connection.db!.collection(
      this.migrationCollectionName,
    );
    await collection.insertOne({
      name: migration.name,
      version: migration.version,
      executedAt: new Date(),
    });
  }

  private async removeMigrationRecord(migration: IMigration): Promise<void> {
    const collection = this.connection.db!.collection(
      this.migrationCollectionName,
    );
    await collection.deleteOne({ name: migration.name });
  }
}
