import { Injectable, OnModuleInit } from '@nestjs/common';
import { MigrationRunnerService } from './migration-runner.service';
import { InitChatCollectionsMigration } from './migrations/001-init-chat-collections.migration';

@Injectable()
export class MigrationService implements OnModuleInit {
  constructor(private readonly migrationRunner: MigrationRunnerService) {}

  onModuleInit() {
    // Register all migrations
    this.registerMigrations();
  }

  private registerMigrations(): void {
    // Register migrations in order
    this.migrationRunner.registerMigration(new InitChatCollectionsMigration());

    // Add more migrations here as needed
    // this.migrationRunner.registerMigration(new AnotherMigration());
  }

  async runMigrations(): Promise<void> {
    await this.migrationRunner.run();
  }

  async rollbackMigrations(steps?: number): Promise<void> {
    await this.migrationRunner.rollback(steps);
  }

  async getMigrationStatus() {
    return await this.migrationRunner.status();
  }
}
