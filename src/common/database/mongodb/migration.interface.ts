import { Connection } from 'mongoose';

export interface IMigration {
  name: string;
  version: string;
  up(connection: Connection): Promise<void>;
  down(connection: Connection): Promise<void>;
}

export interface IMigrationRecord {
  name: string;
  version: string;
  executedAt: Date;
}

export interface IMigrationRunner {
  run(): Promise<void>;
  rollback(steps?: number): Promise<void>;
  status(): Promise<IMigrationRecord[]>;
}
