#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';
import { MigrationService } from './migration.service';
import { MigrationRunnerService } from './migration-runner.service';
import mongodbConfig from './config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [mongodbConfig],
      envFilePath: ['.env'],
    }),
    MongooseModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        uri: configService.get('mongodb.uri'),
        dbName: configService.get('mongodb.dbName'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [MigrationService, MigrationRunnerService],
})
class MigrationModule {}

async function runMigrations() {
  const app = await NestFactory.createApplicationContext(MigrationModule);

  try {
    const migrationService = app.get(MigrationService);

    const command = process.argv[2];

    switch (command) {
      case 'up':
      case 'run':
        console.log('Running MongoDB migrations...');
        await migrationService.runMigrations();
        break;

      case 'down':
      case 'rollback': {
        const steps = parseInt(process.argv[3]) || 1;
        console.log(`Rolling back ${steps} migration(s)...`);
        await migrationService.rollbackMigrations(steps);
        break;
      }

      case 'status': {
        console.log('Migration status:');
        const status = await migrationService.getMigrationStatus();
        if (status.length === 0) {
          console.log('No migrations have been executed.');
        } else {
          status.forEach((migration) => {
            console.log(
              `✓ ${migration.name} (${migration.version}) - ${migration.executedAt.toISOString()}`,
            );
          });
        }
        break;
      }

      default:
        console.log('Usage:');
        console.log(
          '  npm run mongodb:migrate up     - Run pending migrations',
        );
        console.log(
          '  npm run mongodb:migrate down [steps] - Rollback migrations',
        );
        console.log(
          '  npm run mongodb:migrate status  - Show migration status',
        );
        process.exit(1);
    }

    console.log('Migration command completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

if (require.main === module) {
  runMigrations().catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
}
