# MongoDB Setup for Chat Application

This directory contains the MongoDB configuration, schemas, and migration system for the chat application.

## Overview

The chat application uses MongoDB to store:

- **Chatrooms**: Chat room information including participants, settings, and metadata
- **Messages**: Individual chat messages with content, timestamps, and relationships

## Database Schema

### Chatrooms Collection

- `name`: Room name (required, indexed)
- `description`: Optional room description
- `participants`: Array of user ObjectIds (indexed)
- `createdBy`: User ObjectId who created the room (indexed)
- `isPrivate`: Boolean flag for private rooms (indexed)
- `maxParticipants`: Maximum number of participants (default: 100)
- `createdAt`, `updatedAt`: Timestamps

### Messages Collection

- `chatroomId`: Reference to chatroom (indexed)
- `userId`: Reference to user who sent the message (indexed)
- `content`: Message content (text indexed for search)
- `messageType`: Type of message (text, image, file, system)
- `timestamp`: When message was sent (indexed)
- `editedAt`: When message was last edited (optional)
- `isDeleted`: Soft delete flag (indexed)
- `replyTo`: Reference to another message (optional)
- `attachments`: Array of file attachments
- `readBy`: Array of read receipts with userId and timestamp

## Configuration

The MongoDB connection is configured through environment variables:

```env
MONGODB_URI=***********************************************************************
MONGODB_DB_NAME=sit_chat  # Optional, extracted from URI if not provided
```

## Migration System

The migration system helps manage database schema changes and initialization.

### Available Commands

```bash
# Run all pending migrations
npm run mongodb:migrate up

# Rollback the last migration
npm run mongodb:migrate down

# Rollback multiple migrations
npm run mongodb:migrate down 3

# Check migration status
npm run mongodb:migrate status
```

### Creating New Migrations

1. Create a new migration file in `src/common/database/mongodb/migrations/`
2. Follow the naming convention: `XXX-description.migration.ts`
3. Implement the `IMigration` interface:

```typescript
import { Connection } from 'mongoose';
import { IMigration } from '../migration.interface';

export class MyNewMigration implements IMigration {
  name = 'MyNewMigration';
  version = '002';

  async up(connection: Connection): Promise<void> {
    // Migration logic here
  }

  async down(connection: Connection): Promise<void> {
    // Rollback logic here
  }
}
```

4. Register the migration in `migration.service.ts`

## Indexes

The following indexes are automatically created for optimal performance:

### Chatrooms

- `name` (single)
- `participants` (single)
- `createdBy` (single)
- `isPrivate` (single)
- `createdAt` (single, descending)
- `participants + isPrivate` (compound)
- `createdBy + createdAt` (compound)

### Messages

- `chatroomId + timestamp` (compound, descending)
- `userId + timestamp` (compound, descending)
- `chatroomId + messageType` (compound)
- `chatroomId + isDeleted + timestamp` (compound)
- `replyTo` (single)
- `content` (text index for search)

## Usage in Services

To use the MongoDB schemas in your services:

```typescript
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Chatroom, ChatroomDocument } from './schemas/chatroom.schema';
import { Message, MessageDocument } from './schemas/message.schema';

@Injectable()
export class ChatService {
  constructor(
    @InjectModel(Chatroom.name) private chatroomModel: Model<ChatroomDocument>,
    @InjectModel(Message.name) private messageModel: Model<MessageDocument>,
  ) {}

  // Your service methods here
}
```

## Docker Setup

The MongoDB instance is configured in `docker-compose.yml`:

```yaml
mongodb:
  image: mongo:7.0
  environment:
    MONGO_INITDB_ROOT_USERNAME: mongodb_user
    MONGO_INITDB_ROOT_PASSWORD: 123456
    MONGO_INITDB_DATABASE: sit_chat
  ports:
    - '27017:27017'
```

## Initial Setup

1. Start the MongoDB container:

   ```bash
   docker-compose up -d mongodb
   ```

2. Run the initial migrations:

   ```bash
   npm run mongodb:migrate up
   ```

3. Verify the setup:
   ```bash
   npm run mongodb:migrate status
   ```

The database is now ready for use with the chat application!
