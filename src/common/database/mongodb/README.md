# MongoDB Setup for Chat Application

This directory contains the MongoDB migration.

## Migration System

The migration system helps manage database schema changes and initialization.

### Available Commands

```bash
# Run all pending migrations
npm run mongodb:migrate up

# Rollback the last migration
npm run mongodb:migrate down

# Rollback multiple migrations
npm run mongodb:migrate down 3

# Check migration status
npm run mongodb:migrate status
```

### Creating New Migrations

1. Create a new migration file in `src/common/database/mongodb/migrations/`
2. Follow the naming convention: `XXX-description.migration.ts`
3. Implement the `IMigration` interface:

```typescript
import { Connection } from 'mongoose';
import { IMigration } from '../migration.interface';

export class MyNewMigration implements IMigration {
  name = 'MyNewMigration';
  version = '002';

  async up(connection: Connection): Promise<void> {
    // Migration logic here
  }

  async down(connection: Connection): Promise<void> {
    // Rollback logic here
  }
}
```

4. Register the migration in `migration.service.ts`

## Initial Setup

1. Start the MongoDB container:

   ```bash
   docker-compose up -d chat-db
   ```

2. Run the initial migrations:

   ```bash
   npm run mongodb:migrate up
   ```

3. Verify the setup:
   ```bash
   npm run mongodb:migrate status
   ```

The database is now ready for use with the chat application!
