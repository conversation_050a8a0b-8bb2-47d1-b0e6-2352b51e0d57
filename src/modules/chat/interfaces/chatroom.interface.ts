import { Types } from 'mongoose';

export interface IChatroom {
  name: string;
  description?: string;
  participants: Types.ObjectId[];
  createdBy: Types.ObjectId;
  isPrivate: boolean;
  maxParticipants?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateChatroomDto {
  name: string;
  description?: string;
  participants?: string[];
  isPrivate?: boolean;
  maxParticipants?: number;
}

export interface IUpdateChatroomDto {
  name?: string;
  description?: string;
  isPrivate?: boolean;
  maxParticipants?: number;
}

export interface IJoinChatroomDto {
  chatroomId: string;
  userId: string;
}

export interface ILeaveChatroomDto {
  chatroomId: string;
  userId: string;
}
