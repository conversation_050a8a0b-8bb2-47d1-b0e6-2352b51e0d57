import { Types } from 'mongoose';

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  SYSTEM = 'system',
}

export interface IMessage {
  chatroomId: Types.ObjectId;
  userId: Types.ObjectId;
  content: string;
  messageType: MessageType;
  timestamp: Date;
  editedAt?: Date;
  isDeleted: boolean;
  replyTo?: Types.ObjectId;
  attachments?: IMessageAttachment[];
  readBy: IMessageReadStatus[];
}

export interface IMessageAttachment {
  fileName: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
}

export interface IMessageReadStatus {
  userId: Types.ObjectId;
  readAt: Date;
}

export interface ICreateMessageDto {
  chatroomId: string;
  content: string;
  messageType?: MessageType;
  replyTo?: string;
  attachments?: IMessageAttachment[];
}

export interface IUpdateMessageDto {
  content?: string;
  attachments?: IMessageAttachment[];
}

export interface IMarkMessageReadDto {
  messageId: string;
  userId: string;
}

export interface IGetMessagesQuery {
  chatroomId: string;
  page?: number;
  limit?: number;
  before?: string; // message ID for pagination
  after?: string; // message ID for pagination
}
