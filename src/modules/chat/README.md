# MongoDB Setup for Chat Application

This directory contains the MongoDB configuration, schemas, and migration system for the chat application.

## Overview

The chat application uses MongoDB to store:

- **Chatrooms**: Chat room information including participants, settings, and metadata
- **Messages**: Individual chat messages with content, timestamps, and relationships

## Database Schema

### Chatrooms Collection

Each document represents a chatroom.

- `name`: Room name (required, indexed, trimmed, max 100 characters)
- `description`: Optional room description (trimmed, max 500 characters)
- `participants`: Array of user ObjectIds (indexed)
- `createdBy`: User ObjectId who created the room (indexed)
- `isPrivate`: Boolean flag for private rooms (indexed, default: false)
- `maxParticipants`: Maximum number of participants (min: 2, max: 1000, default: 100)
- `createdAt`, `updatedAt`: Timestamps (auto-managed)

### Messages Collection

Each document represents a message sent in a chatroom.

- `chatroomId`: Reference to chatroom (indexed)
- `userId`: Reference to user who sent the message (UUIDv4, indexed)
- `content`: Message content (required, trimmed, max 5000 characters, text indexed for search)
- `messageType`: Type of message - "text", "image", "file", "system" (indexed, default: "text")
- `timestamp`: When message was sent (indexed, default: current time)
- `editedAt`: When message was last edited (optional)
- `isDeleted`: Soft delete flag (indexed, default: false)
- `replyTo`: Reference to another message (optional)
- `attachments`: Array of file attachments (optional, default: empty array)
- `readBy`: Array of read receipts with userId and timestamp (default: empty array)

## Configuration

The MongoDB connection is configured through environment variables:

```env
MONGODB_URI=***********************************************************************
MONGODB_DB_NAME=sit_chat  # Optional, extracted from URI if not provided
```

## Indexes

The following indexes are automatically created for optimal performance:

### Chatrooms

- `name` (single)
- `participants` (single)
- `createdBy` (single)
- `isPrivate` (single)
- `createdAt` (single, descending)
- `participants + isPrivate` (compound)
- `createdBy + createdAt` (compound)

### Messages

- `chatroomId + timestamp` (compound, descending)
- `userId + timestamp` (compound, descending)
- `chatroomId + messageType` (compound)
- `chatroomId + isDeleted + timestamp` (compound)
- `replyTo` (single)
- `content` (text index for search)

## Usage in Services

To use the MongoDB schemas in your services:

```typescript
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Chatroom, ChatroomDocument } from './schemas/chatroom.schema';
import { Message, MessageDocument } from './schemas/message.schema';

@Injectable()
export class ChatService {
  constructor(
    @InjectModel(Chatroom.name) private chatroomModel: Model<ChatroomDocument>,
    @InjectModel(Message.name) private messageModel: Model<MessageDocument>,
  ) {}

  // Your service methods here
}
```
