import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { IChatroom } from '../interfaces/chatroom.interface';

@Schema({
  timestamps: true,
  collection: 'chatrooms',
})
export class Chatroom extends Document implements IChatroom {
  @Prop({
    required: true,
    trim: true,
    maxlength: 100,
    index: true,
  })
  name: string;

  @Prop({
    trim: true,
    maxlength: 500,
  })
  description?: string;

  @Prop({
    type: [{ type: Types.ObjectId, ref: 'User' }],
    default: [],
    index: true,
  })
  participants: Types.ObjectId[];

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  createdBy: Types.ObjectId;

  @Prop({
    type: Boolean,
    default: false,
    index: true,
  })
  isPrivate: boolean;

  @Prop({
    type: Number,
    min: 2,
    max: 1000,
    default: 100,
  })
  maxParticipants?: number;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const ChatroomSchema = SchemaFactory.createForClass(Chatroom);

// Indexes for better performance
ChatroomSchema.index({ name: 1 });
ChatroomSchema.index({ participants: 1 });
ChatroomSchema.index({ createdBy: 1 });
ChatroomSchema.index({ isPrivate: 1 });
ChatroomSchema.index({ createdAt: -1 });

// Compound indexes
ChatroomSchema.index({ participants: 1, isPrivate: 1 });
ChatroomSchema.index({ createdBy: 1, createdAt: -1 });

// Virtual for participant count
ChatroomSchema.virtual('participantCount').get(function () {
  return this.participants.length;
});

// Ensure virtuals are included in JSON output
ChatroomSchema.set('toJSON', { virtuals: true });
ChatroomSchema.set('toObject', { virtuals: true });

export type ChatroomDocument = Chatroom & Document;
