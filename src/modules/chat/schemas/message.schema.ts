import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import {
  IMessage,
  MessageType,
  IMessageAttachment,
  IMessageReadStatus,
} from '../interfaces/message.interface';

@Schema({
  timestamps: false, // We'll handle timestamps manually
  collection: 'messages',
})
export class Message extends Document implements IMessage {
  @Prop({
    type: Types.ObjectId,
    ref: 'Chatroom',
    required: true,
    index: true,
  })
  chatroomId: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  userId: Types.ObjectId;

  @Prop({
    required: true,
    trim: true,
    maxlength: 5000,
  })
  content: string;

  @Prop({
    type: String,
    enum: Object.values(MessageType),
    default: MessageType.TEXT,
    index: true,
  })
  messageType: MessageType;

  @Prop({
    type: Date,
    default: Date.now,
    index: true,
  })
  timestamp: Date;

  @Prop({
    type: Date,
  })
  editedAt?: Date;

  @Prop({
    type: Boolean,
    default: false,
    index: true,
  })
  isDeleted: boolean;

  @Prop({
    type: Types.ObjectId,
    ref: 'Message',
  })
  replyTo?: Types.ObjectId;

  @Prop({
    type: [
      {
        fileName: { type: String, required: true },
        fileUrl: { type: String, required: true },
        fileSize: { type: Number, required: true },
        mimeType: { type: String, required: true },
      },
    ],
    default: [],
  })
  attachments?: IMessageAttachment[];

  @Prop({
    type: [
      {
        userId: { type: Types.ObjectId, ref: 'User', required: true },
        readAt: { type: Date, required: true },
      },
    ],
    default: [],
  })
  readBy: IMessageReadStatus[];
}

export const MessageSchema = SchemaFactory.createForClass(Message);

// Indexes for better performance
MessageSchema.index({ chatroomId: 1, timestamp: -1 });
MessageSchema.index({ userId: 1, timestamp: -1 });
MessageSchema.index({ chatroomId: 1, messageType: 1 });
MessageSchema.index({ chatroomId: 1, isDeleted: 1, timestamp: -1 });
MessageSchema.index({ replyTo: 1 });

// Compound indexes for common queries
MessageSchema.index({ chatroomId: 1, isDeleted: 1, timestamp: -1 });
MessageSchema.index({ userId: 1, chatroomId: 1, timestamp: -1 });

// Text index for message content search
MessageSchema.index({ content: 'text' });

export type MessageDocument = Message & Document;
