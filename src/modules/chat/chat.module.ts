import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Chatroom, ChatroomSchema } from './schemas/chatroom.schema';
import { Message, MessageSchema } from './schemas/message.schema';
import { MongodbModule } from '../../common/database/mongodb/mongodb.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Chatroom.name, schema: ChatroomSchema },
      { name: Message.name, schema: MessageSchema },
    ]),
    MongodbModule,
  ],
  providers: [],
  controllers: [],
  exports: [MongooseModule, MongodbModule],
})
export class ChatModule {}
