const { MongoClient, ObjectId } = require('mongodb');
const { v4: uuidv4 } = require('uuid');

const uri = '***********************************************************************';

const client = new MongoClient(uri);

// Helper function to create dates in the past
function pastDate(daysAgo, hoursAgo = 0, minutesAgo = 0) {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  date.setHours(date.getHours() - hoursAgo);
  date.setMinutes(date.getMinutes() - minutesAgo);
  return date;
}

async function run() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const database = client.db('sit_chat');
    const chatrooms = database.collection('chatrooms');
    const messages = database.collection('messages');

    // Clear existing data
    console.log('Clearing existing data...');
    await chatrooms.deleteMany({});
    await messages.deleteMany({});

    // Sample users with UUIDs
    const users = [
      { userId: uuidv4(), username: 'jdoe', displayName: '<PERSON>', avatar: 'https://ui-avatars.com/api/?name=John+Doe&background=0d8abc&color=fff' },
      { userId: uuidv4(), username: 'asmith', displayName: 'Alice Smith', avatar: 'https://ui-avatars.com/api/?name=Alice+Smith&background=f39c12&color=fff' },
      { userId: uuidv4(), username: 'bjohnson', displayName: 'Bob Johnson', avatar: 'https://ui-avatars.com/api/?name=Bob+Johnson&background=e74c3c&color=fff' },
      { userId: uuidv4(), username: 'cdavis', displayName: 'Carol Davis', avatar: 'https://ui-avatars.com/api/?name=Carol+Davis&background=9b59b6&color=fff' },
      { userId: uuidv4(), username: 'dwilson', displayName: 'David Wilson', avatar: 'https://ui-avatars.com/api/?name=David+Wilson&background=27ae60&color=fff' },
      { userId: uuidv4(), username: 'ebrown', displayName: 'Emma Brown', avatar: 'https://ui-avatars.com/api/?name=Emma+Brown&background=e67e22&color=fff' },
    ];

    console.log('Creating sample users:', users.map(u => u.displayName).join(', '));

    // Create chatrooms with varied timestamps
    const chatroomDocs = [
      {
        name: 'General Discussion',
        description: 'A place for everyone to chat about anything and everything.',
        participants: users,
        createdBy: users[0].userId,
        isPrivate: false,
        maxParticipants: 100,
        createdAt: pastDate(7),
        updatedAt: new Date(),
      },
      {
        name: 'Project Team Alpha',
        description: 'Private discussions for the Alpha project development team.',
        participants: [users[0], users[1], users[2]],
        createdBy: users[0].userId,
        isPrivate: true,
        maxParticipants: 10,
        createdAt: pastDate(5),
        updatedAt: new Date(),
      },
      {
        name: 'Study Group - JavaScript',
        description: 'Let\'s study JavaScript together! Share resources and ask questions.',
        participants: [users[1], users[2], users[3], users[4]],
        createdBy: users[2].userId,
        isPrivate: false,
        maxParticipants: 50,
        createdAt: pastDate(3),
        updatedAt: new Date(),
      },
      {
        name: 'Announcements',
        description: 'Official announcements and important updates.',
        participants: users,
        createdBy: users[0].userId,
        isPrivate: false,
        maxParticipants: 1000,
        createdAt: pastDate(10),
        updatedAt: new Date(),
      },
      {
        name: 'Random Chat',
        description: 'Chat about anything random - pets, weather, food, you name it!',
        participants: [users[0], users[3], users[4], users[5]],
        createdBy: users[3].userId,
        isPrivate: false,
        maxParticipants: 25,
        createdAt: pastDate(2),
        updatedAt: new Date(),
      },
    ];

    console.log('Inserting chatrooms...');
    const insertedChatrooms = await chatrooms.insertMany(chatroomDocs);
    console.log(`Inserted ${Object.keys(insertedChatrooms.insertedIds).length} chatrooms`);

    // Get the chatroom IDs as ObjectIds
    const chatroomIds = Object.values(insertedChatrooms.insertedIds);

    // Create comprehensive message data
    const messageDocs = [];

    // General Discussion messages
    messageDocs.push(
      {
        chatroomId: chatroomIds[0],
        user: users[0],
        content: 'Welcome to the General Discussion! Feel free to chat about anything here.',
        messageType: 'text',
        timestamp: pastDate(7, 0, 5),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[1], readAt: pastDate(7, 0, 3) },
          { user: users[2], readAt: pastDate(7, 0, 2) },
          { user: users[3], readAt: pastDate(6, 23, 58) },
        ],
      },
      {
        chatroomId: chatroomIds[0],
        user: users[1],
        content: 'Thanks John! Great to have a space for everyone to connect.',
        messageType: 'text',
        timestamp: pastDate(7, 0, 2),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[0], readAt: pastDate(7, 0, 1) },
          { user: users[2], readAt: pastDate(6, 23, 59) },
        ],
      },
      {
        chatroomId: chatroomIds[0],
        user: users[2],
        content: 'How is everyone doing today? Beautiful weather outside! ☀️',
        messageType: 'text',
        timestamp: pastDate(2, 3, 30),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[0], readAt: pastDate(2, 3, 25) },
          { user: users[3], readAt: pastDate(2, 3, 20) },
          { user: users[4], readAt: pastDate(2, 2, 45) },
        ],
      },
      {
        chatroomId: chatroomIds[0],
        user: users[4],
        content: 'Agreed! Perfect day for a walk in the park.',
        messageType: 'text',
        timestamp: pastDate(2, 2, 40),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[2], readAt: pastDate(2, 2, 35) },
        ],
      }
    );

    // Project Team Alpha messages (private)
    messageDocs.push(
      {
        chatroomId: chatroomIds[1],
        user: users[0],
        content: 'Team, let\'s discuss the Alpha project milestones for this sprint.',
        messageType: 'text',
        timestamp: pastDate(5, 2, 0),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[1], readAt: pastDate(5, 1, 55) },
          { user: users[2], readAt: pastDate(5, 1, 50) },
        ],
      },
      {
        chatroomId: chatroomIds[1],
        user: users[1],
        content: 'Great! I\'ve completed the user authentication module. Ready for testing.',
        messageType: 'text',
        timestamp: pastDate(5, 1, 45),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[0], readAt: pastDate(5, 1, 40) },
          { user: users[2], readAt: pastDate(5, 1, 35) },
        ],
      },
      {
        chatroomId: chatroomIds[1],
        user: users[2],
        content: 'Excellent work Alice! The database schema is also finalized.',
        messageType: 'text',
        timestamp: pastDate(5, 1, 30),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[0], readAt: pastDate(5, 1, 25) },
          { user: users[1], readAt: pastDate(5, 1, 20) },
        ],
      },
      {
        chatroomId: chatroomIds[1],
        user: users[0],
        content: 'Perfect! Let\'s schedule a demo for tomorrow at 2 PM.',
        messageType: 'text',
        timestamp: pastDate(1, 5, 0),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[1], readAt: pastDate(1, 4, 55) },
        ],
      }
    );

    // Study Group messages
    messageDocs.push(
      {
        chatroomId: chatroomIds[2],
        user: users[2],
        content: 'Welcome to our JavaScript study group! 📚',
        messageType: 'text',
        timestamp: pastDate(3, 1, 0),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[1], readAt: pastDate(3, 0, 55) },
          { user: users[3], readAt: pastDate(3, 0, 50) },
          { user: users[4], readAt: pastDate(3, 0, 45) },
        ],
      },
      {
        chatroomId: chatroomIds[2],
        user: users[3],
        content: 'Can someone explain closures in JavaScript? I\'m still confused about them.',
        messageType: 'text',
        timestamp: pastDate(2, 4, 15),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[2], readAt: pastDate(2, 4, 10) },
          { user: users[4], readAt: pastDate(2, 4, 5) },
        ],
      },
      {
        chatroomId: chatroomIds[2],
        user: users[4],
        content: 'Sure! A closure is when a function has access to variables from an outer scope even after the outer function has returned.',
        messageType: 'text',
        timestamp: pastDate(2, 4, 0),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[2], readAt: pastDate(2, 3, 55) },
          { user: users[3], readAt: pastDate(2, 3, 50) },
        ],
      },
      {
        chatroomId: chatroomIds[2],
        user: users[1],
        content: 'Here\'s a simple example:\n\n```javascript\nfunction outer() {\n  let count = 0;\n  return function inner() {\n    count++;\n    return count;\n  };\n}\nconst counter = outer();\ncounter(); // 1\ncounter(); // 2\n```',
        messageType: 'text',
        timestamp: pastDate(2, 3, 45),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[3], readAt: pastDate(2, 3, 40) },
        ],
      }
    );

    // Announcements messages
    messageDocs.push(
      {
        chatroomId: chatroomIds[3],
        user: users[0],
        content: 'System maintenance scheduled for tonight from 11 PM to 1 AM EST.',
        messageType: 'system',
        timestamp: pastDate(3, 8, 0),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[1], readAt: pastDate(3, 7, 55) },
          { user: users[2], readAt: pastDate(3, 7, 50) },
          { user: users[3], readAt: pastDate(3, 7, 45) },
          { user: users[4], readAt: pastDate(3, 7, 40) },
          { user: users[5], readAt: pastDate(3, 7, 35) },
        ],
      },
      {
        chatroomId: chatroomIds[3],
        user: users[0],
        content: '🎉 New feature released: Dark mode is now available in user settings!',
        messageType: 'text',
        timestamp: pastDate(1, 10, 0),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[1], readAt: pastDate(1, 9, 55) },
          { user: users[2], readAt: pastDate(1, 9, 50) },
        ],
      }
    );

    // Random Chat messages
    messageDocs.push(
      {
        chatroomId: chatroomIds[4],
        user: users[3],
        content: 'Welcome to random chat! Share anything fun here 🎉',
        messageType: 'text',
        timestamp: pastDate(2, 1, 0),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[0], readAt: pastDate(2, 0, 55) },
          { user: users[4], readAt: pastDate(2, 0, 50) },
          { user: users[5], readAt: pastDate(2, 0, 45) },
        ],
      },
      {
        chatroomId: chatroomIds[4],
        user: users[4],
        content: 'Has anyone seen my dog? He\'s a golden retriever named Max 🐕',
        messageType: 'text',
        timestamp: pastDate(1, 6, 30),
        isDeleted: false,
        attachments: [
          {
            fileName: 'max_the_dog.jpg',
            fileUrl: 'https://example.com/images/max_the_dog.jpg',
            fileSize: 245760,
            mimeType: 'image/jpeg',
          },
        ],
        readBy: [
          { user: users[3], readAt: pastDate(1, 6, 25) },
          { user: users[5], readAt: pastDate(1, 6, 20) },
        ],
      },
      {
        chatroomId: chatroomIds[4],
        user: users[5],
        content: 'What a cute dog! I hope you find him soon 💕',
        messageType: 'text',
        timestamp: pastDate(1, 6, 15),
        isDeleted: false,
        attachments: [],
        readBy: [
          { user: users[4], readAt: pastDate(1, 6, 10) },
        ],
      },
      {
        chatroomId: chatroomIds[4],
        user: users[4],
        content: 'Update: Found him! He was at the neighbor\'s house playing with their dog 😅',
        messageType: 'text',
        timestamp: pastDate(0, 2, 0),
        isDeleted: false,
        attachments: [],
        readBy: [],
      }
    );

    console.log('Inserting messages...');
    await messages.insertMany(messageDocs);
    console.log(`Inserted ${messageDocs.length} messages`);

    // Create some message replies
    const existingMessages = await messages.find({}).toArray();
    const replyMessages = [
      {
        chatroomId: chatroomIds[0],
        user: users[5],
        content: 'I completely agree with Bob! Perfect weather for outdoor activities.',
        messageType: 'text',
        timestamp: pastDate(2, 2, 30),
        isDeleted: false,
        replyTo: existingMessages.find(m => m.content.includes('Perfect day for a walk'))._id,
        attachments: [],
        readBy: [],
      },
    ];

    console.log('Inserting reply messages...');
    await messages.insertMany(replyMessages);
    console.log(`Inserted ${replyMessages.length} reply messages`);

    console.log('\n✅ Sample data inserted successfully!');
    console.log('\n📊 Summary:');
    console.log(`   - ${Object.keys(insertedChatrooms.insertedIds).length} chatrooms created`);
    console.log(`   - ${messageDocs.length + replyMessages.length} messages created`);
    console.log(`   - ${users.length} sample users used`);
    console.log('\n🎯 Chatrooms created:');
    chatroomDocs.forEach((room, index) => {
      console.log(`   - "${room.name}" (${room.isPrivate ? 'Private' : 'Public'}, ${room.participants.length} participants)`);
    });
    
  } catch (err) {
    console.error('❌ Error inserting sample data:', err);
  } finally {
    await client.close();
    console.log('\n🔌 Database connection closed.');
  }
}

run().catch(console.dir);
