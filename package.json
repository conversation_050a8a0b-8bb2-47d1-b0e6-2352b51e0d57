{"name": "sit-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prepare": "husky", "typeorm": "env-cmd ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- --dataSource=src/common/database/data-source.ts migration:generate", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- --dataSource=src/common/database/data-source.ts migration:run", "migration:revert": "npm run typeorm -- --dataSource=src/common/database/data-source.ts migration:revert", "schema:drop": "npm run typeorm -- --dataSource=src/common/database/data-source.ts schema:drop", "mongodb:migrate": "env-cmd ts-node -r tsconfig-paths/register ./src/common/database/mongodb/run-migrations.ts", "seed:create": "hygen seeds create", "seed:run": "ts-node -r tsconfig-paths/register ./src/common/database/seeds/run-seed.ts", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint-staged": "lint-staged", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.9", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.1.5", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.0.3", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.5", "@socket.io/redis-adapter": "^8.3.0", "@types/aws-sdk": "^2.7.4", "@types/qrcode": "^1.5.5", "axios": "^1.8.3", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "he": "^1.2.0", "libphonenumber-js": "^1.12.6", "lodash": "^4.17.21", "microservices": "^0.1.0", "mongoose": "^8.16.4", "nestjs-i18n": "^10.5.0", "nestjs-real-ip": "^3.0.1", "nodemailer": "^6.10.0", "nodemailer-express-handlebars": "^7.0.0", "numeral": "^2.0.6", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.13.1", "qrcode": "^1.5.4", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.33.5", "slugify": "^1.6.6", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0", "typeorm-transactional": "^0.5.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "write-excel-file": "^2.3.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/crypto-js": "^4.2.2", "@types/express": "^5.0.0", "@types/he": "^1.2.3", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/nodemailer": "^6.4.17", "@types/nodemailer-express-handlebars": "^4.0.5", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "env-cmd": "^10.1.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.2", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.{ts,js}": ["prettier --write", "eslint --fix"], "*.json": ["prettier --write"]}}